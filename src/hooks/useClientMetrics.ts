import { useState, useEffect } from 'react'
import { supabase, mapClientMetricToAppointment } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { Appointment } from '@/data/mockData'

export const useClientMetrics = () => {
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { profile } = useAuth()

  const fetchMetrics = async () => {
    try {
      setLoading(true)
      setError(null)

      if (!profile) {
        console.log('No profile available, skipping fetch')
        setAppointments([])
        return
      }

      console.log('Fetching metrics for profile:', profile)

      let query = supabase.from('client_metrics_raw').select('*')

      // Filter by company_id for non-super admin users
      if (profile.role !== 'super_admin' && profile.company_id) {
        console.log('Filtering by company_id:', profile.company_id)
        query = query.eq('company_id', profile.company_id)
      } else {
        console.log('Super admin - fetching all data')
      }

      const { data, error: fetchError } = await query

      if (fetchError) {
        console.error('Database fetch error:', fetchError)
        throw fetchError
      }

      console.log('Raw data fetched:', data?.length, 'records')
      console.log('Sample record:', data?.[0])

      // Map the database records to our appointment interface
      const mappedAppointments = (data || []).map(mapClientMetricToAppointment)
      console.log('Mapped appointments:', mappedAppointments.length)
      console.log('Sample mapped appointment:', mappedAppointments[0])

      setAppointments(mappedAppointments)
    } catch (err: any) {
      console.error('Error fetching client metrics:', err)
      setError(err.message)
      setAppointments([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMetrics()
  }, [profile])

  return {
    appointments,
    loading,
    error,
    refetch: fetchMetrics
  }
}
