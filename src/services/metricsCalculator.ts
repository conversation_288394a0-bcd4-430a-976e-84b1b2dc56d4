import { Appointment } from '@/data/mockData'

export interface MetricsResult {
  total: number
  sits: number
  closes: number
  noShows: number
  rescheduled: number
  notInterested: number
  disqualified: number
  followUps: number
  noShowsPercentage: string
  rescheduledPercentage: string
  notInterestedPercentage: string
  disqualifiedPercentage: string
  followUpsPercentage: string
  showRate: string
  closeRate: string
}

/**
 * Enhanced metrics calculator that handles compound dispositions
 * and implements the formulas based on the actual raw data structure
 */
export class MetricsCalculator {
  
  /**
   * Calculate all metrics for a given set of appointments
   */
  static calculateMetrics(appointments: Appointment[]): MetricsResult {
    const total = appointments.length
    
    if (total === 0) {
      return this.getEmptyMetrics()
    }

    // Calculate each metric using the enhanced logic
    const sits = this.calculateSits(appointments)
    const closes = this.calculateCloses(appointments)
    const noShows = this.calculateNoShows(appointments)
    const rescheduled = this.calculateRescheduled(appointments)
    const notInterested = this.calculateNotInterested(appointments)
    const disqualified = this.calculateDisqualified(appointments)
    const followUps = this.calculateFollowUps(appointments)

    // Calculate percentages
    const noShowsPercentage = ((noShows / total) * 100).toFixed(1)
    const rescheduledPercentage = ((rescheduled / total) * 100).toFixed(1)
    const notInterestedPercentage = ((notInterested / total) * 100).toFixed(1)
    const disqualifiedPercentage = ((disqualified / total) * 100).toFixed(1)
    const followUpsPercentage = ((followUps / total) * 100).toFixed(1)
    const showRate = ((sits / total) * 100).toFixed(1)
    const closeRate = ((closes / total) * 100).toFixed(1)

    return {
      total,
      sits,
      closes,
      noShows,
      rescheduled,
      notInterested,
      disqualified,
      followUps,
      noShowsPercentage,
      rescheduledPercentage,
      notInterestedPercentage,
      disqualifiedPercentage,
      followUpsPercentage,
      showRate,
      closeRate
    }
  }

  /**
   * Calculate sits - any disposition containing "Sat"
   */
  private static calculateSits(appointments: Appointment[]): number {
    return appointments.filter(apt => {
      const disposition = apt.confirmation_disposition?.toLowerCase() || ''
      return disposition.includes('sat')
    }).length
  }

  /**
   * Calculate closes - any disposition containing "Closed"
   */
  private static calculateCloses(appointments: Appointment[]): number {
    return appointments.filter(apt => {
      const disposition = apt.confirmation_disposition?.toLowerCase() || ''
      return disposition.includes('closed')
    }).length
  }

  /**
   * Calculate no shows - any disposition containing "No Show"
   */
  private static calculateNoShows(appointments: Appointment[]): number {
    return appointments.filter(apt => {
      const disposition = apt.confirmation_disposition?.toLowerCase() || ''
      return disposition.includes('no show')
    }).length
  }

  /**
   * Calculate rescheduled - any disposition containing "Rescheduled"
   */
  private static calculateRescheduled(appointments: Appointment[]): number {
    return appointments.filter(apt => {
      const disposition = apt.confirmation_disposition?.toLowerCase() || ''
      return disposition.includes('rescheduled')
    }).length
  }

  /**
   * Calculate not interested - any disposition containing "Not Interested"
   */
  private static calculateNotInterested(appointments: Appointment[]): number {
    return appointments.filter(apt => {
      const disposition = apt.confirmation_disposition?.toLowerCase() || ''
      return disposition.includes('not interested')
    }).length
  }

  /**
   * Calculate disqualified - any disposition containing "Disqualified"
   */
  private static calculateDisqualified(appointments: Appointment[]): number {
    return appointments.filter(apt => {
      const disposition = apt.confirmation_disposition?.toLowerCase() || ''
      return disposition.includes('disqualified')
    }).length
  }

  /**
   * Calculate follow-ups - any disposition containing "Follow"
   */
  private static calculateFollowUps(appointments: Appointment[]): number {
    return appointments.filter(apt => {
      const disposition = apt.confirmation_disposition?.toLowerCase() || ''
      return disposition.includes('follow')
    }).length
  }

  /**
   * Return empty metrics object
   */
  private static getEmptyMetrics(): MetricsResult {
    return {
      total: 0,
      sits: 0,
      closes: 0,
      noShows: 0,
      rescheduled: 0,
      notInterested: 0,
      disqualified: 0,
      followUps: 0,
      noShowsPercentage: '0.0',
      rescheduledPercentage: '0.0',
      notInterestedPercentage: '0.0',
      disqualifiedPercentage: '0.0',
      followUpsPercentage: '0.0',
      showRate: '0.0',
      closeRate: '0.0'
    }
  }

  /**
   * Calculate period-over-period comparison
   */
  static calculatePeriodComparison(
    currentPeriodAppointments: Appointment[],
    previousPeriodAppointments: Appointment[]
  ): {
    percentageChange: string
    percentageChangeNumeric: number
  } {
    const currentTotal = currentPeriodAppointments.length
    const previousTotal = previousPeriodAppointments.length
    
    const percentageChangeNumeric = previousTotal > 0 
      ? ((currentTotal - previousTotal) / previousTotal) * 100 
      : 0
    
    const percentageChange = percentageChangeNumeric.toFixed(1)
    
    return {
      percentageChange,
      percentageChangeNumeric
    }
  }
}
