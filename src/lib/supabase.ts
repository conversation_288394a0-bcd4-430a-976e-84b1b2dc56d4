import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database types
export interface Database {
  public: {
    Tables: {
      companies: {
        Row: {
          id: string
          name: string
          company_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          company_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          company_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      user_profiles: {
        Row: {
          id: string
          user_id: string
          company_id: string | null
          role: 'super_admin' | 'admin' | 'user'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          company_id?: string | null
          role?: 'super_admin' | 'admin' | 'user'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          company_id?: string | null
          role?: 'super_admin' | 'admin' | 'user'
          created_at?: string
          updated_at?: string
        }
      }
      client_metrics_raw: {
        Row: {
          id: string
          client_name: string | null
          company_id: string | null
          name: string
          closer_name: string
          booked_for: string
          confirmation_disposition: string
          note: string
          phone_number: string
          address: string
          setter_name: string
          setter_number: string
          email: string
          disposition_date: string
          site_survey: string
          m1_commission: number
          m2_commission: number
          contact_link: string
          recording_media_link: string
          credit_score: number
          roof_type: string
          existing_solar: boolean
          shading: string
          appointment_type: string
          confirmed: boolean
          contact_ID: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          client_name?: string | null
          company_id?: string | null
          name: string
          closer_name: string
          booked_for: string
          confirmation_disposition: string
          note: string
          phone_number: string
          address: string
          setter_name: string
          setter_number: string
          email: string
          disposition_date: string
          site_survey: string
          m1_commission: number
          m2_commission: number
          contact_link: string
          recording_media_link: string
          credit_score: number
          roof_type: string
          existing_solar: boolean
          shading: string
          appointment_type: string
          confirmed: boolean
          contact_ID: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          client_name?: string | null
          company_id?: string | null
          name?: string
          closer_name?: string
          booked_for?: string
          confirmation_disposition?: string
          note?: string
          phone_number?: string
          address?: string
          setter_name?: string
          setter_number?: string
          email?: string
          disposition_date?: string
          site_survey?: string
          m1_commission?: number
          m2_commission?: number
          contact_link?: string
          recording_media_link?: string
          credit_score?: number
          roof_type?: string
          existing_solar?: boolean
          shading?: string
          appointment_type?: string
          confirmed?: boolean
          contact_ID?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}

export type Company = Database['public']['Tables']['companies']['Row']
export type UserProfile = Database['public']['Tables']['user_profiles']['Row']
export type ClientMetric = Database['public']['Tables']['client_metrics_raw']['Row']

// Helper function to parse date from various formats
const parseAppointmentDate = (dateStr: string): string => {
  if (!dateStr) return new Date().toISOString();

  try {
    // Handle M/d/yyyy H:mm:ss format (e.g., "7/16/2025 19:30:00")
    if (dateStr.includes('/')) {
      // Split date and time parts
      const [datePart, timePart] = dateStr.split(' ');
      const [month, day, year] = datePart.split('/');

      if (timePart) {
        // Parse with time
        const [hours, minutes, seconds] = timePart.split(':');
        const date = new Date(
          parseInt(year),
          parseInt(month) - 1,
          parseInt(day),
          parseInt(hours) || 0,
          parseInt(minutes) || 0,
          parseInt(seconds) || 0
        );
        return date.toISOString();
      } else {
        // Parse date only
        const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        return date.toISOString();
      }
    }

    // Handle other formats or already ISO format
    const date = new Date(dateStr);
    return date.toISOString();
  } catch (error) {
    console.warn('Failed to parse date:', dateStr, error);
    return new Date().toISOString();
  }
};

// Helper function to map database columns to our interface
export const mapClientMetricToAppointment = (metric: any) => {
  return {
    name: metric.name || '',
    closer_name: metric.closer_name || '',
    booked_for: parseAppointmentDate(metric.booked_for || ''),
    confirmation_disposition: metric.confirmation_disposition || 'Pending',
    note: metric.note || '',
    phone_number: metric.phone_number?.toString() || '',
    address: metric.address || '',
    setter_name: metric.setter_name || '',
    setter_number: metric.setter_number || '',
    email: metric.email || '',
    disposition_date: metric.disposition_date || '',
    site_survey: metric.site_survey || '',
    m1_commission: parseFloat(metric.m1_commission) || 0,
    m2_commission: parseFloat(metric.m2_commission) || 0,
    contact_link: metric.contact_link || '',
    recording_media_link: metric.recording_media_link || '',
    credit_score: parseInt(metric.credit_score) || 0,
    roof_type: metric.roof_type || '',
    existing_solar: metric.existing_solar === true,
    shading: metric.shading || '',
    appointment_type: metric.appointment_type || '',
    confirmed: metric.confirmed === true,
    contact_ID: metric.contact_ID || '',
    client_name: metric.client_name || '',
    company_id: metric.company_id || ''
  }
}
