import { Building2, LogOut, Shield, Settings } from "lucide-react";
import { MetricsVisibilityPanel } from "./MetricsVisibilityPanel";
import { Button } from "@/components/ui/button";
import { User } from "@supabase/supabase-js";
import { UserProfile } from "@/lib/supabase";
import { useNavigate } from "react-router-dom";

interface DashboardHeaderProps {
  clientName?: string;
  logoUrl?: string;
  visibleMetrics?: Record<string, boolean>;
  onToggleMetric?: (metric: string) => void;
  user?: User | null;
  profile?: UserProfile | null;
  onSignOut?: () => void;
}

export function DashboardHeader({
  clientName,
  logoUrl = "[INSERT_LOGO_URL_HERE]",
  visibleMetrics,
  onToggleMetric,
  user,
  profile,
  onSignOut
}: DashboardHeaderProps) {
  const navigate = useNavigate();

  const getDisplayName = () => {
    if (profile?.role === 'super_admin') {
      return "Admin Dashboard";
    }
    return clientName || profile?.company_id || "Client Dashboard";
  };

  const handleAdminPanelClick = () => {
    navigate('/admin');
  };
  return (
    <header className="w-full bg-card border-b border-border px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <img 
            src="/lovable-uploads/58030552-ef5b-484d-bb0e-66450b796c1d.png" 
            alt="Premura Call Center Dashboard" 
            className="h-12 w-12 rounded-lg"
            onError={(e) => {
              // Fallback to icon if image fails to load
              const fallback = document.createElement('div');
              fallback.className = 'h-12 w-12 bg-gradient-primary rounded-lg flex items-center justify-center';
              fallback.innerHTML = '<svg class="h-8 w-8 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/></svg>';
              (e.target as HTMLImageElement).parentNode?.replaceChild(fallback, e.target as HTMLImageElement);
            }}
          />
          <div>
            <h1 className="text-xl font-bold text-foreground">Premura Call Center Dashboard</h1>
            <p className="text-sm text-muted-foreground">Analytics Dashboard</p>
          </div>
        </div>
        
        <div className="flex items-center">
          <p className="text-lg font-bold text-primary">{getDisplayName()}</p>
        </div>

        <div className="flex items-center space-x-4">
          {visibleMetrics && onToggleMetric && (
            <MetricsVisibilityPanel
              visibleMetrics={visibleMetrics}
              onToggleMetric={onToggleMetric}
            />
          )}

          {user && (
            <div className="flex items-center space-x-3">
              <div className="text-right">
                <p className="text-sm font-medium text-foreground">{user.email}</p>
                {profile && (
                  <div className="flex items-center space-x-1">
                    {profile.role === 'super_admin' && <Shield className="h-3 w-3 text-primary" />}
                    <p className="text-xs text-muted-foreground">
                      {profile.role.replace('_', ' ').toUpperCase()}
                    </p>
                  </div>
                )}
              </div>

              {profile?.role === 'super_admin' && (
                <Button variant="outline" size="sm" onClick={handleAdminPanelClick}>
                  <Settings className="h-4 w-4 mr-2" />
                  Admin Panel
                </Button>
              )}

              {onSignOut && (
                <Button variant="outline" size="sm" onClick={onSignOut}>
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </header>
  );
}