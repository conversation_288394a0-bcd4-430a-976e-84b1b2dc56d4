import React from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { LoginForm } from './LoginForm'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'super_admin' | 'admin' | 'user'
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole
}) => {
  const { user, profile, loading } = useAuth()

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dashboard-bg">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  // If no user, show login form
  if (!user) {
    console.log('ProtectedRoute: No user found, showing login form')
    return <LoginForm />
  }

  // If user exists but no profile yet, wait a bit more
  if (user && !profile) {
    console.log('ProtectedRoute: User exists but no profile, waiting...')
    return (
      <div className="min-h-screen flex items-center justify-center bg-dashboard-bg">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading profile...</span>
        </div>
      </div>
    )
  }

  // Check role requirements
  if (requiredRole && profile?.role !== requiredRole && profile?.role !== 'super_admin') {
    console.log(`ProtectedRoute: Access denied. Required: ${requiredRole}, User has: ${profile?.role}`)
    return (
      <div className="min-h-screen flex items-center justify-center bg-dashboard-bg">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">Access Denied</h1>
          <p className="text-muted-foreground">
            You don't have permission to access this page.
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Required role: {requiredRole}, Your role: {profile?.role}
          </p>
        </div>
      </div>
    )
  }

  console.log(`ProtectedRoute: Access granted. User: ${user.email}, Role: ${profile?.role}`)
  return <>{children}</>
}
