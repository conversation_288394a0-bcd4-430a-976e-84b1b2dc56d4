import React, { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { LoginForm } from './LoginForm'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'super_admin' | 'admin' | 'user'
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole
}) => {
  const { user, profile, loading } = useAuth()
  const [profileWaitTime, setProfileWaitTime] = useState(0)

  // Track how long we've been waiting for profile
  useEffect(() => {
    if (user && !profile && !loading) {
      const interval = setInterval(() => {
        setProfileWaitTime(prev => prev + 1)
      }, 1000)

      return () => clearInterval(interval)
    } else {
      setProfileWaitTime(0)
    }
  }, [user, profile, loading])

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dashboard-bg">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  // If no user, show login form
  if (!user) {
    console.log('ProtectedRoute: No user found, showing login form')
    return <LoginForm />
  }

  // If user exists but no profile yet, wait up to 10 seconds
  if (user && !profile) {
    console.log(`ProtectedRoute: User exists but no profile, waiting... (${profileWaitTime}s)`)

    // After 10 seconds, allow access with default permissions if no specific role required
    if (profileWaitTime >= 10) {
      console.log('ProtectedRoute: Profile loading timeout, proceeding with limited access')

      // If a specific role is required and we can't load profile, deny access
      if (requiredRole) {
        return (
          <div className="min-h-screen flex items-center justify-center bg-dashboard-bg">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-foreground mb-4">Profile Loading Error</h1>
              <p className="text-muted-foreground mb-4">
                Unable to load your profile. Please try refreshing the page.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
              >
                Refresh Page
              </button>
            </div>
          </div>
        )
      }

      // Allow access for non-role-specific routes
      console.log('ProtectedRoute: Allowing access without profile for non-role-specific route')
      return <>{children}</>
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-dashboard-bg">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading profile... ({profileWaitTime}s)</span>
        </div>
      </div>
    )
  }

  // Check role requirements
  if (requiredRole && profile?.role !== requiredRole && profile?.role !== 'super_admin') {
    console.log(`ProtectedRoute: Access denied. Required: ${requiredRole}, User has: ${profile?.role}`)
    return (
      <div className="min-h-screen flex items-center justify-center bg-dashboard-bg">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-4">Access Denied</h1>
          <p className="text-muted-foreground">
            You don't have permission to access this page.
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Required role: {requiredRole}, Your role: {profile?.role}
          </p>
        </div>
      </div>
    )
  }

  console.log(`ProtectedRoute: Access granted. User: ${user.email}, Role: ${profile?.role}`)
  return <>{children}</>
}
