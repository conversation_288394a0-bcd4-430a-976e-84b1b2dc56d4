import React from 'react'
import { useAuth } from '@/contexts/AuthContext'

export const AuthDebug: React.FC = () => {
  const { user, profile, session, loading, testProfileFetch } = useAuth()

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">Auth Debug Info</h3>
      <div className="space-y-1">
        <div>Loading: {loading ? 'true' : 'false'}</div>
        <div>User: {user ? user.email : 'null'}</div>
        <div>Profile: {profile ? `${profile.role} (${profile.id})` : 'null'}</div>
        <div>Session: {session ? 'active' : 'null'}</div>
        {user && (
          <div className="mt-2 pt-2 border-t border-gray-600">
            <div>User ID: {user.id}</div>
            <div>Created: {new Date(user.created_at).toLocaleString()}</div>
            <button
              onClick={() => testProfileFetch && testProfileFetch()}
              className="mt-2 px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
            >
              Test Profile Fetch
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
