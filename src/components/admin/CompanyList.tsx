import React from 'react'
import { Company } from '@/lib/supabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Building2, Calendar } from 'lucide-react'
import { format } from 'date-fns'

interface CompanyListProps {
  companies: Company[]
  onUpdate: () => void
}

export const CompanyList: React.FC<CompanyListProps> = ({ companies }) => {
  if (companies.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Companies Yet</h3>
          <p className="text-muted-foreground text-center">
            Create your first company to get started with client management.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {companies.map((company) => (
        <Card key={company.id}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <Building2 className="h-5 w-5" />
                <span>{company.name}</span>
              </CardTitle>
              <Badge variant="secondary">{company.company_id}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>Created: {format(new Date(company.created_at), 'MMM dd, yyyy')}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
