import React, { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, X } from 'lucide-react'
import { toast } from 'sonner'

interface CompanyFormProps {
  onSuccess: () => void
  onCancel: () => void
}

export const CompanyForm: React.FC<CompanyFormProps> = ({ onSuccess, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    company_id: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // Validate form data
      if (!formData.name.trim() || !formData.company_id.trim()) {
        throw new Error('Please fill in all fields')
      }

      // Check if company_id already exists
      const { data: existingCompany } = await supabase
        .from('companies')
        .select('company_id')
        .eq('company_id', formData.company_id)
        .single()

      if (existingCompany) {
        throw new Error('Company ID already exists. Please choose a different one.')
      }

      // Create the company
      const { error } = await supabase
        .from('companies')
        .insert([{
          name: formData.name.trim(),
          company_id: formData.company_id.trim()
        }])

      if (error) throw error

      onSuccess()
    } catch (err: any) {
      setError(err.message)
      toast.error(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    // Clear error when user starts typing
    if (error) setError(null)
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Create New Company</CardTitle>
            <CardDescription>
              Add a new client company to the system
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="company-name">Company Name</Label>
            <Input
              id="company-name"
              type="text"
              placeholder="Enter company name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              required
              disabled={loading}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="company-id">Company ID</Label>
            <Input
              id="company-id"
              type="text"
              placeholder="Enter unique company ID"
              value={formData.company_id}
              onChange={(e) => handleInputChange('company_id', e.target.value)}
              required
              disabled={loading}
            />
            <p className="text-xs text-muted-foreground">
              This will be used to filter dashboard data for this company
            </p>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex space-x-2">
            <Button type="submit" className="flex-1" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Company'
              )}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
