import React from 'react'
import { UserProfile } from '@/lib/supabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { User, Calendar, Building2 } from 'lucide-react'
import { format } from 'date-fns'

interface UserListProps {
  users: UserProfile[]
  onUpdate: () => void
}

export const UserList: React.FC<UserListProps> = ({ users }) => {
  if (users.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <User className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Users Yet</h3>
          <p className="text-muted-foreground text-center">
            Create your first user account to get started.
          </p>
        </CardContent>
      </Card>
    )
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'destructive'
      case 'admin':
        return 'default'
      case 'user':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  return (
    <div className="space-y-4">
      {users.map((user) => (
        <Card key={user.id}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>{user.user_id}</span>
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Badge variant={getRoleBadgeVariant(user.role)}>
                  {user.role.replace('_', ' ').toUpperCase()}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {user.company_id && (
                <div className="flex items-center space-x-2 text-sm">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <span>Company: {user.company_id}</span>
                  {(user as any).companies && (
                    <Badge variant="outline">
                      {(user as any).companies.name}
                    </Badge>
                  )}
                </div>
              )}
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>Created: {format(new Date(user.created_at), 'MMM dd, yyyy')}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
